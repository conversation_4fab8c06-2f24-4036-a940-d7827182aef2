-- Migration: Remove company verification filters from materialized views
-- Company verification has been deprecated - only benefits are verified now
-- This migration removes verification_status filters from materialized views

-- Drop and recreate companies_with_benefits_cache without verification filter
DROP MATERIALIZED VIEW IF EXISTS companies_with_benefits_cache CASCADE;

CREATE MATERIALIZED VIEW companies_with_benefits_cache AS
SELECT 
    c.id,
    c.name,
    c.description,
    c.website,
    c.logo_url,
    c.size,
    c.industry,
    c.founded_year,
    c.verification_status,
    c.created_at,
    c.updated_at,
    COALESCE(json_agg(DISTINCT jsonb_build_object('id', cb.id, 'benefit_id', cb.benefit_id, 'is_verified', cb.is_verified, 'added_by', cb.added_by, 'created_at', cb.created_at, 'benefit', jsonb_build_object('id', b.id, 'name', b.name, 'category_id', b.category_id, 'icon', b.icon, 'category', jsonb_build_object('id', bc.id, 'name', bc.name, 'display_name', bc.display_name, 'icon', bc.icon, 'description', bc.description)))) FILTER (WHERE (cb.id IS NOT NULL)), '[]'::json) AS company_benefits,
    COALESCE(json_agg(DISTINCT jsonb_build_object('id', cl.id, 'location_type', cl.location_type, 'city', cl.city, 'country', cl.country, 'country_code', cl.country_code, 'is_primary', cl.is_primary, 'is_headquarters', cl.is_headquarters, 'location_raw', cl.location_raw, 'location_normalized', cl.location_normalized)) FILTER (WHERE (cl.id IS NOT NULL)), '[]'::json) AS locations
FROM companies c
LEFT JOIN company_benefits cb ON c.id = cb.company_id
LEFT JOIN benefits b ON cb.benefit_id = b.id
LEFT JOIN benefit_categories bc ON b.category_id = bc.id
LEFT JOIN company_locations cl ON c.id = cl.company_id
-- REMOVED: WHERE c.verification_status = 'verified'
GROUP BY c.id, c.name, c.description, c.website, c.logo_url, c.size, c.industry, c.founded_year, c.verification_status, c.created_at, c.updated_at
WITH NO DATA;

-- Drop and recreate benefits_with_categories_cache without verification filter
DROP MATERIALIZED VIEW IF EXISTS benefits_with_categories_cache CASCADE;

CREATE MATERIALIZED VIEW benefits_with_categories_cache AS
SELECT 
    b.id,
    b.name,
    b.category_id,
    b.icon,
    b.description,
    b.created_at,
    bc.name AS category,
    bc.display_name AS category_display_name,
    bc.icon AS category_icon,
    bc.description AS category_description,
    count(cb.company_id) AS company_count
FROM benefits b
LEFT JOIN benefit_categories bc ON b.category_id = bc.id
LEFT JOIN company_benefits cb ON b.id = cb.benefit_id
LEFT JOIN companies c ON cb.company_id = c.id
-- REMOVED: WHERE c.verification_status = 'verified'
GROUP BY b.id, b.name, b.category_id, b.icon, b.description, b.created_at, bc.name, bc.display_name, bc.icon, bc.description
ORDER BY b.name
WITH NO DATA;

-- Refresh the materialized views
REFRESH MATERIALIZED VIEW benefits_with_categories_cache;
REFRESH MATERIALIZED VIEW companies_with_benefits_cache;

-- Recreate the indexes
CREATE UNIQUE INDEX IF NOT EXISTS idx_companies_with_benefits_cache_id ON companies_with_benefits_cache(id);
CREATE INDEX IF NOT EXISTS idx_companies_with_benefits_cache_name ON companies_with_benefits_cache USING gin(to_tsvector('english', name));
CREATE INDEX IF NOT EXISTS idx_companies_with_benefits_cache_industry ON companies_with_benefits_cache(industry);
CREATE INDEX IF NOT EXISTS idx_companies_with_benefits_cache_size ON companies_with_benefits_cache(size);

CREATE UNIQUE INDEX IF NOT EXISTS idx_benefits_with_categories_cache_id ON benefits_with_categories_cache(id);
CREATE INDEX IF NOT EXISTS idx_benefits_with_categories_cache_category ON benefits_with_categories_cache(category_id);
CREATE INDEX IF NOT EXISTS idx_benefits_with_categories_cache_name ON benefits_with_categories_cache USING gin(to_tsvector('english', name));

-- Add comment to document the change
COMMENT ON MATERIALIZED VIEW companies_with_benefits_cache IS 'All companies with their benefits and locations - company verification has been deprecated';
COMMENT ON MATERIALIZED VIEW benefits_with_categories_cache IS 'All benefits with categories and company counts - company verification has been deprecated';
