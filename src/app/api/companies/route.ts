import { NextRequest, NextResponse } from 'next/server'
import { getCompanies, createCompany } from '@/lib/database'
import { requireAuth, getCurrentUser } from '@/lib/auth'
import { logCompanyAdded } from '@/lib/activity-logger'
import { withLocationSearch } from '@/lib/location-middleware'
import type { CompanySize } from '@/types/database'

export async function GET(request: NextRequest) {
  return withLocationSearch(request, async (params) => {
    try {
      const filters = {
        location: typeof params.location === 'string' ? params.location : undefined,
        normalizedLocation: params.normalizedLocation || undefined,
        size: (params.size as CompanySize) || undefined,
        industry: typeof params.industry === 'string' ? params.industry.split(',').filter(Boolean) : undefined,
        benefits: typeof params.benefits === 'string' ? params.benefits.split(',').filter(Boolean) : undefined,
        search: typeof params.search === 'string' ? params.search : undefined,
      }

      // Get pagination parameters
      const page = parseInt(params.page as string) || 1
      const limit = parseInt(params.limit as string) || 20
      const offset = (page - 1) * limit

      const allCompanies = await getCompanies(filters)

      // Apply pagination
      const companies = allCompanies.slice(offset, offset + limit)
      const total = allCompanies.length

      const response = {
        companies,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }

      return new Response(JSON.stringify(response), {
        headers: { 'Content-Type': 'application/json' }
      })
    } catch (error) {
      console.error('Error fetching companies:', error)
      return new Response(
        JSON.stringify({ error: 'Failed to fetch companies' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      )
    }
  })
}

export async function POST(request: NextRequest) {
  try {
    const _user = await requireAuth()

    const body = await request.json()
    const { name, size, industry, description, domain, career_url } = body

    if (!name || !size || !industry) {
      return NextResponse.json(
        { error: 'Missing required fields (name, size, industry)' },
        { status: 400 }
      )
    }

    const company = await createCompany({
      name,
      size,
      industry,
      description,
      domain: domain?.toLowerCase(),
      career_url: career_url || '',
    })

    // Get user details for activity logging
    const currentUser = await getCurrentUser()

    // Log the company addition activity
    await logCompanyAdded(
      company.id,
      company.name,
      currentUser?.id,
      currentUser?.email,
      `${currentUser?.firstName || ''} ${currentUser?.lastName || ''}`.trim() || undefined
    )

    return NextResponse.json(company, { status: 201 })
  } catch (error) {
    console.error('Error creating company:', error)
    return NextResponse.json(
      { error: 'Failed to create company' },
      { status: 500 }
    )
  }
}
