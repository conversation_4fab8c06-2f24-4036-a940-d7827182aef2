import { query } from './local-db'
import {
  getCache,
  setCache,
  clearCachePattern as _clearCachePattern,
  invalidateCompanyCache as invalidateCompanyCacheInternal,
  invalidateBenefitCache as invalidateBenefitCacheInternal,
  getCachedCompaniesWithBenefits as _getCachedCompaniesWithBenefits,
  getCachedBenefitsWithCategories as _getCachedBenefitsWithCategories
} from './postgresql-cache'
import type {
  Company,
  Benefit,
  CompanySize,
  CompanyLocation,
  LocationType
} from '@/types/database'
import { normalizeLocation, type LocationData } from './location-normalization'

// Cache invalidation helpers
async function invalidateCompanyCache(companyId?: string) {
  await invalidateCompanyCacheInternal(companyId)
}

export async function invalidateBenefitCache() {
  await invalidateBenefitCacheInternal()
}

// Company operations
export async function getCompanies(filters?: {
  location?: string
  normalizedLocation?: { city: string; country: string; countryCode: string }
  size?: CompanySize
  industry?: string[]
  benefits?: string[]
  search?: string
}) {
  let sql = `
    SELECT
      c.*,
      COALESCE(benefits_agg.company_benefits, '[]'::json) as company_benefits,
      COALESCE(locations_agg.locations, '[]'::json) as locations
    FROM companies c
    LEFT JOIN (
      SELECT
        cb.company_id,
        json_agg(
          json_build_object(
            'id', cb.id,
            'benefit_id', cb.benefit_id,
            'is_verified', cb.is_verified,
            'benefit', json_build_object(
              'id', b.id,
              'name', b.name,
              'category_id', b.category_id,
              'icon', b.icon
            )
          ) ORDER BY cb.created_at ASC
        ) as company_benefits
      FROM company_benefits cb
      JOIN benefits b ON cb.benefit_id = b.id
      GROUP BY cb.company_id
    ) benefits_agg ON c.id = benefits_agg.company_id
    LEFT JOIN (
      SELECT
        cl.company_id,
        json_agg(
          json_build_object(
            'id', cl.id,
            'company_id', cl.company_id,
            'location_raw', cl.location_raw,
            'location_normalized', cl.location_normalized,
            'city', cl.city,
            'country', cl.country,
            'country_code', cl.country_code,
            'latitude', cl.latitude,
            'longitude', cl.longitude,
            'is_primary', cl.is_primary,
            'is_headquarters', cl.is_headquarters,
            'location_type', cl.location_type,
            'created_at', cl.created_at,
            'updated_at', cl.updated_at
          ) ORDER BY cl.is_primary DESC, cl.is_headquarters DESC, cl.created_at ASC
        ) as locations
      FROM company_locations cl
      GROUP BY cl.company_id
    ) locations_agg ON c.id = locations_agg.company_id
  `

  const conditions: string[] = []
  const params: unknown[] = []
  let paramIndex = 1

  // Only show verified companies
  conditions.push(`c.verification_status = 'verified'`)

  if (filters?.location || filters?.normalizedLocation) {
    if (filters.normalizedLocation) {
      // Use normalized location data for more precise matching
      const { city, country: _country, countryCode: _countryCode } = filters.normalizedLocation
      conditions.push(`EXISTS (
        SELECT 1 FROM company_locations cl2
        WHERE cl2.company_id = c.id
        AND (cl2.city = $${paramIndex}
             OR cl2.city ILIKE $${paramIndex + 1})
      )`)
      params.push(city, `%${city}%`)
      paramIndex += 2
    } else if (filters.location) {
      // Search in company_locations table only
      conditions.push(`EXISTS (
        SELECT 1 FROM company_locations cl2
        WHERE cl2.company_id = c.id
        AND (cl2.city ILIKE $${paramIndex}
             OR cl2.country ILIKE $${paramIndex})
      )`)
      params.push(`%${filters.location}%`)
      paramIndex++
    }
  }

  if (filters?.size) {
    conditions.push(`c.size = $${paramIndex}`)
    params.push(filters.size)
    paramIndex++
  }

  if (filters?.industry && filters.industry.length > 0) {
    const industryConditions = filters.industry.map(() => {
      const condition = `c.industry = $${paramIndex}`
      paramIndex++
      return condition
    })
    conditions.push(`(${industryConditions.join(' OR ')})`)
    params.push(...filters.industry)
  }

  if (filters?.search) {
    // Search company name, description, and locations
    conditions.push(`(c.name ILIKE $${paramIndex}
                     OR c.description ILIKE $${paramIndex}
                     OR EXISTS (
                       SELECT 1 FROM company_locations cl3
                       WHERE cl3.company_id = c.id
                       AND (cl3.city ILIKE $${paramIndex}
                            OR cl3.country ILIKE $${paramIndex})
                     ))`)
    params.push(`%${filters.search}%`)
    paramIndex++
  }

  if (conditions.length > 0) {
    sql += ` WHERE ${conditions.join(' AND ')}`
  }

  sql += ` ORDER BY c.name`

  const result = await query(sql, params)
  let companies = result.rows

  // Filter by benefits if specified
  if (filters?.benefits && filters.benefits.length > 0) {
    companies = companies.filter(company => {
      const companyBenefitNames = company.company_benefits
        ?.map((cb: { benefit?: { name?: string } }) => cb.benefit?.name)
        .filter(Boolean) || []

      return filters.benefits!.some(filterBenefit => {
        // Try exact match first
        if (companyBenefitNames.some((name: string) => name === filterBenefit)) {
          return true
        }
        // Try case-insensitive match
        if (companyBenefitNames.some((name: string) => name.toLowerCase() === filterBenefit.toLowerCase())) {
          return true
        }
        // Try partial match for flexibility
        return companyBenefitNames.some((name: string) =>
          name.toLowerCase().includes(filterBenefit.toLowerCase()) ||
          filterBenefit.toLowerCase().includes(name.toLowerCase())
        )
      })
    })
  }

  return companies
}

export async function getCompanyById(id: string) {
  // Check cache first
  const cacheKey = `company:${id}`
  const cached = await getCache(cacheKey)
  if (cached) {
    return cached
  }

  const sql = `
    SELECT
      c.*,
      COALESCE(benefits_agg.company_benefits, '[]'::json) as company_benefits,
      COALESCE(locations_agg.locations, '[]'::json) as locations
    FROM companies c
    LEFT JOIN (
      SELECT
        cb.company_id,
        json_agg(
          json_build_object(
            'id', cb.id,
            'benefit_id', cb.benefit_id,
            'is_verified', cb.is_verified,
            'added_by', cb.added_by,
            'created_at', cb.created_at,
            'benefit', json_build_object(
              'id', b.id,
              'name', b.name,
              'category_id', b.category_id,
              'icon', b.icon
            )
          ) ORDER BY cb.created_at ASC
        ) as company_benefits
      FROM company_benefits cb
      JOIN benefits b ON cb.benefit_id = b.id
      WHERE cb.company_id = $1
      GROUP BY cb.company_id
    ) benefits_agg ON c.id = benefits_agg.company_id
    LEFT JOIN (
      SELECT
        cl.company_id,
        json_agg(
          json_build_object(
            'id', cl.id,
            'company_id', cl.company_id,
            'location_raw', cl.location_raw,
            'location_normalized', cl.location_normalized,
            'city', cl.city,
            'country', cl.country,
            'country_code', cl.country_code,
            'latitude', cl.latitude,
            'longitude', cl.longitude,
            'is_primary', cl.is_primary,
            'is_headquarters', cl.is_headquarters,
            'location_type', cl.location_type,
            'created_at', cl.created_at,
            'updated_at', cl.updated_at
          ) ORDER BY cl.is_primary DESC, cl.is_headquarters DESC, cl.created_at ASC
        ) as locations
      FROM company_locations cl
      WHERE cl.company_id = $1
      GROUP BY cl.company_id
    ) locations_agg ON c.id = locations_agg.company_id
    WHERE c.id = $1
  `

  const result = await query(sql, [id])
  const company = result.rows[0] || null

  // Cache for 10 minutes
  if (company) {
    await setCache(cacheKey, company, 600)
  }

  return company
}

export async function createCompany(company: Omit<Company, 'id' | 'created_at' | 'updated_at' | 'location'>) {
  const result = await query(
    `INSERT INTO companies (name, size, industry, description, domain, career_url)
     VALUES ($1, $2, $3, $4, $5, $6) RETURNING *`,
    [company.name, company.size, company.industry, company.description, company.domain, company.career_url]
  )

  const newCompany = result.rows[0]

  // Note: Locations should be added separately using addCompanyLocation function
  // This function no longer handles location creation

  return newCompany
}

export async function updateCompany(id: string, updates: Partial<Company>) {
  const keys = Object.keys(updates).filter(key => updates[key as keyof typeof updates] !== undefined && key !== 'updated_at')
  const values = keys.map(key => updates[key as keyof typeof updates])
  const setClause = keys.map((key, i) => `${key} = $${i + 1}`).join(', ')

  const result = await query(
    `UPDATE companies SET ${setClause}, updated_at = NOW() WHERE id = $${keys.length + 1} RETURNING *`,
    [...values, id]
  )
  return result.rows[0]
}

// Company location operations
export async function getCompanyLocations(companyId: string): Promise<CompanyLocation[]> {
  const result = await query(
    `SELECT * FROM company_locations
     WHERE company_id = $1
     ORDER BY is_primary DESC, is_headquarters DESC, created_at ASC`,
    [companyId]
  )
  return result.rows
}

export async function addCompanyLocation(
  companyId: string,
  locationRaw: string,
  locationType: LocationType = 'office',
  isPrimary: boolean = false,
  isHeadquarters: boolean = false,
  locationNormalized?: string,
  city?: string,
  country?: string,
  countryCode?: string,
  latitude?: number,
  longitude?: number
): Promise<CompanyLocation> {
  // Use pre-normalized data if provided, otherwise normalize
  let locationData
  if (locationNormalized && city && country && countryCode) {
    locationData = {
      normalized: locationNormalized,
      city,
      country,
      countryCode,
      latitude,
      longitude
    }
  } else {
    // Normalize the location
    locationData = await normalizeLocation(locationRaw)
  }

  // If this is being set as primary, unset other primary locations
  if (isPrimary) {
    await query(
      'UPDATE company_locations SET is_primary = false WHERE company_id = $1',
      [companyId]
    )
  }

  // If this is being set as headquarters, unset other headquarters
  if (isHeadquarters) {
    await query(
      'UPDATE company_locations SET is_headquarters = false WHERE company_id = $1',
      [companyId]
    )
  }

  const result = await query(
    `INSERT INTO company_locations
     (company_id, location_raw, location_normalized, city, country, country_code,
      latitude, longitude, is_primary, is_headquarters, location_type)
     VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
     RETURNING *`,
    [
      companyId,
      locationRaw,
      locationData.normalized,
      locationData.city,
      locationData.country,
      locationData.countryCode,
      locationData.latitude || null,
      locationData.longitude || null,
      isPrimary,
      isHeadquarters,
      locationType
    ]
  )

  // Invalidate company cache
  await invalidateCompanyCache(companyId)

  return result.rows[0]
}

export async function updateCompanyLocation(
  locationId: string,
  updates: Partial<Pick<CompanyLocation, 'location_raw' | 'is_primary' | 'is_headquarters' | 'location_type'>>
): Promise<CompanyLocation> {
  // Get the current location to know which company it belongs to
  const currentLocation = await query(
    'SELECT company_id FROM company_locations WHERE id = $1',
    [locationId]
  )

  if (currentLocation.rows.length === 0) {
    throw new Error('Location not found')
  }

  const companyId = currentLocation.rows[0].company_id

  // Handle location normalization if location_raw is being updated
  let normalizedData: LocationData | null = null
  if (updates.location_raw) {
    normalizedData = await normalizeLocation(updates.location_raw)
  }

  // If setting as primary, unset other primary locations
  if (updates.is_primary) {
    await query(
      'UPDATE company_locations SET is_primary = false WHERE company_id = $1 AND id != $2',
      [companyId, locationId]
    )
  }

  // If setting as headquarters, unset other headquarters
  if (updates.is_headquarters) {
    await query(
      'UPDATE company_locations SET is_headquarters = false WHERE company_id = $1 AND id != $2',
      [companyId, locationId]
    )
  }

  // Build the update query
  const updateFields: Record<string, unknown> = { ...updates }
  if (normalizedData) {
    updateFields.location_normalized = normalizedData.normalized
    updateFields.city = normalizedData.city
    updateFields.country = normalizedData.country
    updateFields.country_code = normalizedData.countryCode
    updateFields.latitude = normalizedData.latitude || null
    updateFields.longitude = normalizedData.longitude || null
  }

  const keys = Object.keys(updateFields).filter(key => updateFields[key] !== undefined && key !== 'updated_at')
  const values = keys.map(key => updateFields[key])
  const setClause = keys.map((key, i) => `${key} = $${i + 1}`).join(', ')

  const result = await query(
    `UPDATE company_locations SET ${setClause}, updated_at = NOW() WHERE id = $${keys.length + 1} RETURNING *`,
    [...values, locationId]
  )

  // Invalidate company cache
  await invalidateCompanyCache(companyId)

  return result.rows[0]
}

export async function removeCompanyLocation(locationId: string): Promise<void> {
  // Get the company ID before deletion for cache invalidation
  const locationResult = await query(
    'SELECT company_id FROM company_locations WHERE id = $1',
    [locationId]
  )

  if (locationResult.rows.length === 0) {
    throw new Error('Location not found')
  }

  const companyId = locationResult.rows[0].company_id

  await query('DELETE FROM company_locations WHERE id = $1', [locationId])

  // Invalidate company cache
  await invalidateCompanyCache(companyId)
}

// Alias for consistency with API naming
export const deleteCompanyLocation = removeCompanyLocation

// Benefit operations
export async function getBenefits(category?: string) {
  // Check cache first
  const cacheKey = category ? `benefits:category:${category}` : 'benefits:all'
  const cached = await getCache(cacheKey)
  if (cached) {
    return cached
  }

  let sql = `
    SELECT
      b.*,
      bc.name as category_name,
      bc.display_name as category_display_name,
      bc.icon as category_icon
    FROM benefits b
    LEFT JOIN benefit_categories bc ON b.category_id = bc.id
  `
  const params: unknown[] = []

  if (category) {
    // Support both legacy category names and new category IDs
    sql += ' WHERE (bc.name = $1 OR b.category_id = $1)'
    params.push(category)
  }

  sql += ' ORDER BY b.name'

  const result = await query(sql, params)

  // Cache for 30 minutes
  await setCache(cacheKey, result.rows, 1800)

  return result.rows
}

export async function createBenefit(benefit: Omit<Benefit, 'id' | 'created_at'>) {
  const result = await query(
    'INSERT INTO benefits (name, category_id, icon, description) VALUES ($1, $2, $3, $4) RETURNING *',
    [benefit.name, benefit.category_id, benefit.icon, benefit.description]
  )
  return result.rows[0]
}

// Benefit category operations
export async function getBenefitCategories(includeInactive = false) {
  let sql = 'SELECT * FROM benefit_categories'

  if (!includeInactive) {
    sql += ' WHERE is_active = true'
  }

  sql += ' ORDER BY sort_order ASC, display_name ASC'

  const result = await query(sql)
  return result.rows
}

export async function createBenefitCategory(category: {
  name: string
  display_name: string
  description?: string
  icon?: string
  sort_order?: number
}) {
  const result = await query(
    `INSERT INTO benefit_categories (name, display_name, description, icon, sort_order, is_active)
     VALUES ($1, $2, $3, $4, $5, true) RETURNING *`,
    [category.name, category.display_name, category.description, category.icon, category.sort_order || 0]
  )
  return result.rows[0]
}

// Company benefit operations
export async function addCompanyBenefit(companyId: string, benefitId: string, addedBy?: string) {
  const result = await query(
    `INSERT INTO company_benefits (company_id, benefit_id, added_by, is_verified)
     VALUES ($1, $2, $3, $4) RETURNING *`,
    [companyId, benefitId, addedBy, false]
  )
  return result.rows[0]
}

export async function removeCompanyBenefit(companyId: string, benefitId: string) {
  await query(
    'DELETE FROM company_benefits WHERE company_id = $1 AND benefit_id = $2',
    [companyId, benefitId]
  )
}

export async function verifyCompanyBenefit(companyBenefitId: string, isVerified: boolean) {
  const result = await query(
    'UPDATE company_benefits SET is_verified = $1 WHERE id = $2 RETURNING *',
    [isVerified, companyBenefitId]
  )
  return result.rows[0]
}

export async function getCompanyBenefits(companyId: string) {
  const result = await query(
    `SELECT
       cb.*,
       b.name,
       b.category_id,
       b.icon,
       bc.name as category,
       bc.name as category_name,
       bc.display_name as category_display_name,
       CASE
         WHEN cb.added_by IS NULL OR cb.added_by = 'system' OR cb.added_by LIKE '%admin%'
         THEN true
         ELSE false
       END as is_admin_verified
     FROM company_benefits cb
     JOIN benefits b ON cb.benefit_id = b.id
     LEFT JOIN benefit_categories bc ON b.category_id = bc.id
     WHERE cb.company_id = $1
     ORDER BY b.name`,
    [companyId]
  )
  return result.rows
}

export async function bulkAddCompanyBenefits(companyId: string, benefitIds: string[], addedBy?: string) {
  const values = benefitIds.map((benefitId, index) =>
    `($1, $${index + 2}, $${benefitIds.length + 2}, false)`
  ).join(', ')

  const params = [companyId, ...benefitIds, addedBy]

  const result = await query(
    `INSERT INTO company_benefits (company_id, benefit_id, added_by, is_verified)
     VALUES ${values}
     ON CONFLICT (company_id, benefit_id) DO NOTHING
     RETURNING *`,
    params
  )
  return result.rows
}

export async function bulkRemoveCompanyBenefits(companyId: string, benefitIds: string[]) {
  const placeholders = benefitIds.map((_, index) => `$${index + 2}`).join(', ')

  await query(
    `DELETE FROM company_benefits
     WHERE company_id = $1 AND benefit_id IN (${placeholders})`,
    [companyId, ...benefitIds]
  )
}

export async function updateCompanyBenefit(companyBenefitId: string, updates: {
  is_verified?: boolean
  added_by?: string
}) {
  const keys = Object.keys(updates).filter(key => updates[key as keyof typeof updates] !== undefined)
  const values = keys.map(key => updates[key as keyof typeof updates])
  const setClause = keys.map((key, i) => `${key} = $${i + 1}`).join(', ')

  const result = await query(
    `UPDATE company_benefits SET ${setClause} WHERE id = $${keys.length + 1} RETURNING *`,
    [...values, companyBenefitId]
  )
  return result.rows[0]
}



// Search functions
export async function searchCompanies(searchQuery: string) {
  const sql = `
    SELECT
      c.*,
      COALESCE(benefits_agg.company_benefits, '[]'::json) as company_benefits,
      COALESCE(locations_agg.locations, '[]'::json) as locations
    FROM companies c
    LEFT JOIN (
      SELECT
        cb.company_id,
        json_agg(
          json_build_object(
            'id', cb.id,
            'benefit_id', cb.benefit_id,
            'is_verified', cb.is_verified,
            'benefit', json_build_object(
              'id', b.id,
              'name', b.name,
              'category_id', b.category_id,
              'icon', b.icon
            )
          ) ORDER BY cb.created_at ASC
        ) as company_benefits
      FROM company_benefits cb
      JOIN benefits b ON cb.benefit_id = b.id
      GROUP BY cb.company_id
    ) benefits_agg ON c.id = benefits_agg.company_id
    LEFT JOIN (
      SELECT
        cl.company_id,
        json_agg(
          json_build_object(
            'id', cl.id,
            'company_id', cl.company_id,
            'location_raw', cl.location_raw,
            'location_normalized', cl.location_normalized,
            'city', cl.city,
            'country', cl.country,
            'country_code', cl.country_code,
            'latitude', cl.latitude,
            'longitude', cl.longitude,
            'is_primary', cl.is_primary,
            'is_headquarters', cl.is_headquarters,
            'location_type', cl.location_type,
            'created_at', cl.created_at,
            'updated_at', cl.updated_at
          ) ORDER BY cl.is_primary DESC, cl.is_headquarters DESC, cl.created_at ASC
        ) as locations
      FROM company_locations cl
      GROUP BY cl.company_id
    ) locations_agg ON c.id = locations_agg.company_id
    WHERE c.name ILIKE $1
       OR c.description ILIKE $1
       OR EXISTS (
         SELECT 1 FROM company_locations cl2
         WHERE cl2.company_id = c.id
         AND (cl2.city ILIKE $1
              OR cl2.country ILIKE $1)
       )
    ORDER BY c.name
    LIMIT 20
  `

  const result = await query(sql, [`%${searchQuery}%`])
  return result.rows
}

export async function getCompanyByDomain(domain: string) {
  const result = await query(
    'SELECT * FROM companies WHERE domain = $1',
    [domain.toLowerCase()]
  )
  return result.rows[0] || null
}
